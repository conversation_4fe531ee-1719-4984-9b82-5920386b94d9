#!/usr/bin/env python3
"""
使用训练好的YOLOv5模型进行目标检测

使用方法:
    python detect_yqjdataset.py --source path/to/images --weights path/to/best.pt

这个脚本会:
1. 使用训练好的模型对图像进行检测
2. 保存检测结果
3. 显示检测统计信息
"""

import os
import sys
from pathlib import Path
import argparse

# 添加yolov5-master到Python路径
yolov5_path = Path(__file__).parent / "yolov5-master"
sys.path.append(str(yolov5_path))

# 切换到yolov5目录
os.chdir(yolov5_path)

# 导入YOLOv5检测模块
from detect import main, parse_opt

def detect_yqjdataset(source_path=None, weights_path=None):
    """使用训练好的模型进行检测"""
    
    if weights_path is None:
        # 默认使用最新训练的权重
        weights_path = "runs/train/yqjdataset_exp/weights/best.pt"
    
    if source_path is None:
        # 默认使用测试集的一些图像
        source_path = "../../yqjdataset/test/images"
    
    # 设置检测参数
    args = [
        '--weights', weights_path,         # 模型权重
        '--source', source_path,           # 输入图像路径
        '--img', '640',                    # 图像尺寸
        '--conf-thres', '0.25',            # 置信度阈值
        '--iou-thres', '0.45',             # NMS IoU阈值
        '--device', '0',                   # 使用GPU 0
        '--project', 'runs/detect',        # 检测结果目录
        '--name', 'yqjdataset_detect',     # 检测实验名称
        '--save-txt',                      # 保存检测结果为txt
        '--save-conf',                     # 保存置信度
        '--save-crop',                     # 保存检测到的目标裁剪图
        '--line-thickness', '2',           # 边框线条粗细
        '--hide-labels',                   # 隐藏标签（可选）
        '--hide-conf',                     # 隐藏置信度（可选）
    ]
    
    # 解析参数
    sys.argv = ['detect.py'] + args
    opt = parse_opt()
    
    print("开始使用训练好的YOLOv5模型进行检测...")
    print(f"模型权重: {opt.weights}")
    print(f"输入源: {opt.source}")
    print(f"图像尺寸: {opt.imgsz}")
    print(f"置信度阈值: {opt.conf_thres}")
    print(f"IoU阈值: {opt.iou_thres}")
    print("-" * 50)
    
    # 开始检测
    main(opt)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--source', type=str, default=None, 
                       help='source path (default: ../../yqjdataset/test/images)')
    parser.add_argument('--weights', type=str, default=None, 
                       help='path to model weights (default: runs/train/yqjdataset_exp/weights/best.pt)')
    args = parser.parse_args()
    
    detect_yqjdataset(args.source, args.weights)
