# Ultralytics YOLO 🚀, AGPL-3.0 license
# Ultralytics Contributor License Agreement (CLA) action https://docs.ultralytics.com/help/CLA
# This workflow automatically requests Pull Requests (PR) authors to sign the Ultralytics CLA before PRs can be merged

name: CLA Assistant
on:
  issue_comment:
    types:
      - created
  pull_request_target:
    types:
      - reopened
      - opened
      - synchronize

permissions:
  actions: write
  contents: write
  pull-requests: write
  statuses: write

jobs:
  CLA:
    if: github.repository == 'ultralytics/yolov5'
    runs-on: ubuntu-latest
    steps:
      - name: CLA Assistant
        if: (github.event.comment.body == 'recheck' || github.event.comment.body == 'I have read the CLA Document and I sign the CLA') || github.event_name == 'pull_request_target'
        uses: contributor-assistant/github-action@v2.4.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # Must be repository secret PAT
          PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
        with:
          path-to-signatures: "signatures/version1/cla.json"
          path-to-document: "https://docs.ultralytics.com/help/CLA" # CLA document
          # Branch must not be protected
          branch: "cla-signatures"
          allowlist: dependabot[bot],github-actions,[pre-commit*,pre-commit*,bot*

          remote-organization-name: ultralytics
          remote-repository-name: cla
          custom-pr-sign-comment: "I have read the CLA Document and I sign the CLA"
          custom-allsigned-prcomment: All Contributors have signed the CLA. ✅
