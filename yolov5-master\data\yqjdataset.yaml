# Ultralytics YOLOv5 🚀, AGPL-3.0 license
# YQJ Dataset configuration
# Example usage: python train.py --data yqjdataset.yaml
# Dataset structure:
# ├── yqjdataset
#     ├── train
#     │   ├── images
#     │   └── labels
#     └── test
#         ├── images
#         └── labels

# Train/val/test sets as 1) dir: path/to/imgs, 2) file: path/to/imgs.txt, or 3) list: [path/to/imgs1, path/to/imgs2, ..]
path: ../yqjdataset  # dataset root dir (relative to yolov5-master)
train: train/images     # train images (relative to 'path') 4161 images
val: test/images        # val images (relative to 'path') 1040 images  
test: test/images       # test images (optional)

# Classes (47 classes: 0-46)
nc: 47

# Class names
names:
  0: '0'
  1: '1'
  2: '2'
  3: '3'
  4: '4'
  5: '5'
  6: '6'
  7: '7'
  8: '8'
  9: '9'
  10: '10'
  11: '11'
  12: '12'
  13: '13'
  14: '14'
  15: '15'
  16: '16'
  17: '17'
  18: '18'
  19: '19'
  20: '20'
  21: '21'
  22: '22'
  23: '23'
  24: '24'
  25: '25'
  26: '26'
  27: '27'
  28: '28'
  29: '29'
  30: '30'
  31: '31'
  32: '32'
  33: '33'
  34: '34'
  35: '35'
  36: '36'
  37: '37'
  38: '38'
  39: '39'
  40: '40'
  41: '41'
  42: '42'
  43: '43'
  44: '44'
  45: '45'
  46: '46'
