#!/usr/bin/env python3
"""
测试yqjdataset配置是否正确

这个脚本会:
1. 验证数据集路径是否存在
2. 检查配置文件是否正确
3. 验证图像和标签文件是否匹配
4. 显示数据集统计信息
"""

import os
import yaml
from pathlib import Path

def test_yqjdataset_config():
    """测试yqjdataset配置"""
    
    print("=" * 60)
    print("YQJDataset 配置测试")
    print("=" * 60)
    
    # 1. 检查配置文件
    config_path = Path("yolov5-master/data/yqjdataset.yaml")
    if not config_path.exists():
        print("❌ 配置文件不存在:", config_path)
        return False
    
    print("✅ 配置文件存在:", config_path)
    
    # 2. 读取配置文件
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print("✅ 配置文件读取成功")
    except Exception as e:
        print("❌ 配置文件读取失败:", e)
        return False
    
    # 3. 检查配置内容
    required_keys = ['path', 'train', 'val', 'nc', 'names']
    for key in required_keys:
        if key not in config:
            print(f"❌ 配置文件缺少必要字段: {key}")
            return False
    
    print("✅ 配置文件包含所有必要字段")
    
    # 4. 检查数据集路径
    dataset_root = Path(config['path'])
    if not dataset_root.is_absolute():
        # 相对路径，相对于yolov5-master目录
        dataset_root = Path("yolov5-master") / dataset_root
    
    if not dataset_root.exists():
        print("❌ 数据集根目录不存在:", dataset_root)
        return False
    
    print("✅ 数据集根目录存在:", dataset_root)
    
    # 5. 检查训练和验证路径
    train_path = dataset_root / config['train']
    val_path = dataset_root / config['val']
    
    if not train_path.exists():
        print("❌ 训练图像目录不存在:", train_path)
        return False
    
    if not val_path.exists():
        print("❌ 验证图像目录不存在:", val_path)
        return False
    
    print("✅ 训练图像目录存在:", train_path)
    print("✅ 验证图像目录存在:", val_path)
    
    # 6. 检查标签目录
    train_labels_path = train_path.parent / "labels"
    val_labels_path = val_path.parent / "labels"
    
    if not train_labels_path.exists():
        print("❌ 训练标签目录不存在:", train_labels_path)
        return False
    
    if not val_labels_path.exists():
        print("❌ 验证标签目录不存在:", val_labels_path)
        return False
    
    print("✅ 训练标签目录存在:", train_labels_path)
    print("✅ 验证标签目录存在:", val_labels_path)
    
    # 7. 统计文件数量
    train_images = list(train_path.glob("*.jpg")) + list(train_path.glob("*.png")) + list(train_path.glob("*.jpeg"))
    val_images = list(val_path.glob("*.jpg")) + list(val_path.glob("*.png")) + list(val_path.glob("*.jpeg"))
    train_labels = list(train_labels_path.glob("*.txt"))
    val_labels = list(val_labels_path.glob("*.txt"))
    
    print(f"\n📊 数据集统计:")
    print(f"   训练图像: {len(train_images)} 张")
    print(f"   训练标签: {len(train_labels)} 个")
    print(f"   验证图像: {len(val_images)} 张")
    print(f"   验证标签: {len(val_labels)} 个")
    print(f"   类别数量: {config['nc']}")
    
    # 8. 检查图像和标签是否匹配
    train_image_names = {img.stem for img in train_images}
    train_label_names = {lbl.stem for lbl in train_labels}
    val_image_names = {img.stem for img in val_images}
    val_label_names = {lbl.stem for lbl in val_labels}
    
    train_missing_labels = train_image_names - train_label_names
    val_missing_labels = val_image_names - val_label_names
    
    if train_missing_labels:
        print(f"⚠️  训练集中有 {len(train_missing_labels)} 张图像缺少对应标签")
    else:
        print("✅ 训练集图像和标签完全匹配")
    
    if val_missing_labels:
        print(f"⚠️  验证集中有 {len(val_missing_labels)} 张图像缺少对应标签")
    else:
        print("✅ 验证集图像和标签完全匹配")
    
    # 9. 显示类别信息
    print(f"\n🏷️  类别信息:")
    if isinstance(config['names'], dict):
        for i, name in config['names'].items():
            print(f"   {i}: {name}")
    elif isinstance(config['names'], list):
        for i, name in enumerate(config['names']):
            print(f"   {i}: {name}")
    
    print("\n" + "=" * 60)
    print("✅ 配置测试完成！数据集配置正确，可以开始训练。")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    test_yqjdataset_config()
