#!/usr/bin/env python3
"""
训练YOLOv5模型使用yqjdataset数据集

使用方法:
    python train_yqjdataset.py

这个脚本会:
1. 使用yqjdataset数据集训练YOLOv5模型
2. 使用预训练的yolov5s.pt权重
3. 训练100个epoch
4. 图像尺寸为640x640
"""

import os
import sys
from pathlib import Path

# 添加yolov5-master到Python路径
yolov5_path = Path(__file__).parent / "yolov5-master"
sys.path.append(str(yolov5_path))

# 切换到yolov5目录
os.chdir(yolov5_path)

# 导入YOLOv5训练模块
from train import main, parse_opt

def train_yqjdataset():
    """使用yqjdataset训练YOLOv5模型"""
    
    # 设置训练参数
    args = [
        '--data', 'data/yqjdataset.yaml',  # 使用我们创建的yqjdataset配置
        '--weights', 'yolov5s.pt',         # 使用预训练权重
        '--img', '640',                    # 图像尺寸
        '--epochs', '100',                 # 训练轮数
        '--batch-size', '16',              # 批次大小
        '--device', '0',                   # 使用GPU 0 (如果有的话)
        '--project', 'runs/train',         # 项目目录
        '--name', 'yqjdataset_exp',        # 实验名称
        '--save-period', '10',             # 每10个epoch保存一次
    ]
    
    # 解析参数
    sys.argv = ['train.py'] + args
    opt = parse_opt()
    
    print("开始训练YOLOv5模型使用yqjdataset数据集...")
    print(f"数据集配置: {opt.data}")
    print(f"预训练权重: {opt.weights}")
    print(f"图像尺寸: {opt.imgsz}")
    print(f"训练轮数: {opt.epochs}")
    print(f"批次大小: {opt.batch_size}")
    print("-" * 50)
    
    # 开始训练
    main(opt)

if __name__ == "__main__":
    train_yqjdataset()
