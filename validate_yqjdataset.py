#!/usr/bin/env python3
"""
验证YOLOv5模型在yqjdataset数据集上的性能

使用方法:
    python validate_yqjdataset.py --weights path/to/best.pt

这个脚本会:
1. 在yqjdataset测试集上验证模型
2. 计算mAP等指标
3. 生成验证报告
"""

import os
import sys
from pathlib import Path
import argparse

# 添加yolov5-master到Python路径
yolov5_path = Path(__file__).parent / "yolov5-master"
sys.path.append(str(yolov5_path))

# 切换到yolov5目录
os.chdir(yolov5_path)

# 导入YOLOv5验证模块
from val import main, parse_opt

def validate_yqjdataset(weights_path=None):
    """在yqjdataset上验证YOLOv5模型"""
    
    if weights_path is None:
        # 默认使用最新训练的权重
        weights_path = "runs/train/yqjdataset_exp/weights/best.pt"
    
    # 设置验证参数
    args = [
        '--data', 'data/yqjdataset.yaml',  # 使用yqjdataset配置
        '--weights', weights_path,         # 模型权重
        '--img', '640',                    # 图像尺寸
        '--batch-size', '32',              # 验证批次大小
        '--device', '0',                   # 使用GPU 0
        '--project', 'runs/val',           # 验证结果目录
        '--name', 'yqjdataset_val',        # 验证实验名称
        '--save-txt',                      # 保存预测结果
        '--save-conf',                     # 保存置信度
        '--verbose',                       # 详细输出
    ]
    
    # 解析参数
    sys.argv = ['val.py'] + args
    opt = parse_opt()
    
    print("开始验证YOLOv5模型在yqjdataset数据集上的性能...")
    print(f"数据集配置: {opt.data}")
    print(f"模型权重: {opt.weights}")
    print(f"图像尺寸: {opt.imgsz}")
    print(f"批次大小: {opt.batch_size}")
    print("-" * 50)
    
    # 开始验证
    main(opt)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--weights', type=str, default=None, 
                       help='path to model weights (default: runs/train/yqjdataset_exp/weights/best.pt)')
    args = parser.parse_args()
    
    validate_yqjdataset(args.weights)
