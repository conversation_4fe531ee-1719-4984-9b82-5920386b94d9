#!/usr/bin/env python3
"""
启动YOLOv5训练的简单脚本

这个脚本会检查环境并启动训练
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """检查必要的依赖"""
    print("检查环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = ['torch', 'torchvision', 'yaml', 'opencv-python', 'numpy', 'matplotlib']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'opencv-python':
                import cv2
            elif package == 'yaml':
                import yaml
            else:
                __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n请安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def start_training():
    """启动训练"""
    print("\n" + "="*60)
    print("开始训练YOLOv5模型使用yqjdataset数据集")
    print("="*60)
    
    # 检查环境
    if not check_requirements():
        print("环境检查失败，请先安装必要的依赖")
        return
    
    # 切换到yolov5目录
    yolov5_dir = Path("yolov5-master")
    if not yolov5_dir.exists():
        print("❌ yolov5-master目录不存在")
        return
    
    os.chdir(yolov5_dir)
    
    # 构建训练命令
    cmd = [
        sys.executable, "train.py",
        "--data", "data/yqjdataset.yaml",
        "--weights", "yolov5s.pt",
        "--img", "640",
        "--epochs", "100",
        "--batch-size", "16",
        "--project", "runs/train",
        "--name", "yqjdataset_exp",
        "--save-period", "10",
    ]
    
    print("训练命令:")
    print(" ".join(cmd))
    print("\n训练参数:")
    print("- 数据集: yqjdataset (4160训练图像, 1040验证图像)")
    print("- 预训练权重: yolov5s.pt")
    print("- 图像尺寸: 640x640")
    print("- 训练轮数: 100")
    print("- 批次大小: 16")
    print("- 结果保存: runs/train/yqjdataset_exp/")
    
    print("\n开始训练...")
    print("注意: 训练可能需要较长时间，请耐心等待")
    print("可以按 Ctrl+C 停止训练")
    print("-"*60)
    
    try:
        # 启动训练
        subprocess.run(cmd, check=True)
        print("\n✅ 训练完成！")
        print("结果保存在: runs/train/yqjdataset_exp/")
        print("最佳权重: runs/train/yqjdataset_exp/weights/best.pt")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 训练失败: {e}")
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")

if __name__ == "__main__":
    start_training()
